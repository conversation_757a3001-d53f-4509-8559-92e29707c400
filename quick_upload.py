#!/usr/bin/env python3
"""
快速 JSON 文件上传脚本
简化版本，用于快速测试上传功能
"""

import os
import glob
import time

def find_json_files():
    """查找当前目录下的 JSON 文件"""
    files = glob.glob("*.json")
    # 过滤掉可能的临时文件
    json_files = [f for f in files if not f.startswith('.') and 'node_modules' not in f]
    return json_files

def simulate_upload(file_path, target_path):
    """模拟上传过程（用于测试）"""
    file_size = os.path.getsize(file_path)
    print(f"  📤 上传: {file_path} -> {target_path}")
    print(f"  📊 大小: {file_size} 字节 ({file_size/1024:.2f} KB)")
    
    # 模拟上传时间
    time.sleep(0.5)
    return True

def main():
    """主函数"""
    print("🔍 扫描 JSON 文件...")
    
    json_files = find_json_files()
    
    if not json_files:
        print("❌ 没有找到 JSON 文件")
        return
    
    print(f"✅ 找到 {len(json_files)} 个 JSON 文件:")
    for file in json_files:
        print(f"  - {file}")
    
    print(f"\n🚀 开始上传到 OBS...")
    print("📍 目标位置: satworld-resource/website_public/json_files/")
    
    success_count = 0
    
    for i, file_path in enumerate(json_files, 1):
        print(f"\n[{i}/{len(json_files)}] 处理文件: {file_path}")
        
        # 生成目标路径
        target_path = f"website_public/json_files/{file_path}"
        
        try:
            # 这里可以替换为实际的上传逻辑
            if simulate_upload(file_path, target_path):
                print(f"  ✅ 上传成功")
                success_count += 1
            else:
                print(f"  ❌ 上传失败")
        except Exception as e:
            print(f"  ❌ 上传异常: {str(e)}")
    
    print(f"\n📊 上传完成!")
    print(f"✅ 成功: {success_count}/{len(json_files)}")
    print(f"❌ 失败: {len(json_files) - success_count}/{len(json_files)}")
    
    if success_count == len(json_files):
        print("🎉 所有文件上传成功!")
    else:
        print("⚠️  部分文件上传失败，请检查日志")

if __name__ == "__main__":
    print("=" * 50)
    print("📦 JSON 文件上传工具 (测试版)")
    print("=" * 50)
    main()
    print("=" * 50)
