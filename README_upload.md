# JSON 文件上传工具

这个 Python 脚本基于现有的 `obsService.js` 逻辑，实现了将当前目录下所有 `.json` 文件上传到华为云 OBS 的功能。

## 功能特性

- 🔍 自动扫描当前目录下的所有 `.json` 文件（排除 `node_modules` 目录）
- 📤 批量上传文件到华为云 OBS
- 🔄 支持重试机制（最多3次重试，指数退避）
- 📊 详细的上传进度和结果报告
- 🛡️ 文件完整性验证（MD5 校验）
- 📝 完整的日志记录
- 🎯 基于现有 `obsService.js` 的配置和逻辑

## 安装依赖

```bash
# 安装 Python 依赖
pip install -r requirements.txt

# 或者直接安装华为云 OBS SDK
pip install esdk-obs-python
```

## 配置说明

脚本使用与 `src/utils/obsEnv.js` 相同的配置：

- **Access Key**: `VOG0TZ1NKSUHGPSWA6HS`
- **Secret Key**: `gzUzXAuFtq2rkBixaZAFnjCYOGii393SiYQeXMW0`
- **Endpoint**: `obs.ap-southeast-1.myhuaweicloud.com`
- **Bucket**: `satworld-resource`
- **Region**: `ap-southeast-1`

上传的文件将存储在 `website_public/json_files/` 路径下。

## 使用方法

```bash
# 运行上传脚本
python upload_json_files.py
```

## 输出文件

脚本运行后会生成以下文件：

1. **`upload_log.txt`** - 详细的上传日志
2. **`upload_report.txt`** - 上传结果报告

## 脚本功能对应关系

| JavaScript 函数 (obsService.js) | Python 方法 | 功能描述 |
|--------------------------------|-------------|----------|
| `retry()` | `retry_operation()` | 重试机制 |
| `uploadObject()` | `upload_single_file()` | 单文件上传 |
| `getObsClient()` | `initialize_client()` | OBS 客户端初始化 |
| `OBS_CONFIG` | `OBSConfig` | 配置管理 |

## 示例输出

```
2024-01-15 10:30:00 - INFO - 开始执行 JSON 文件上传任务...
2024-01-15 10:30:01 - INFO - 成功连接到 OBS，找到 5 个存储桶
2024-01-15 10:30:01 - INFO - 找到 2 个 JSON 文件: ['./package.json', './package-lock.json']
2024-01-15 10:30:01 - INFO - 开始上传 2 个 JSON 文件...
2024-01-15 10:30:01 - INFO - [1/2] 正在处理: ./package.json
2024-01-15 10:30:02 - INFO - 成功上传文件: 'website_public/json_files/package.json'
2024-01-15 10:30:02 - INFO - ✓ 成功上传: ./package.json
2024-01-15 10:30:02 - INFO - [2/2] 正在处理: ./package-lock.json
2024-01-15 10:30:03 - INFO - 成功上传文件: 'website_public/json_files/package-lock.json'
2024-01-15 10:30:03 - INFO - ✓ 成功上传: ./package-lock.json
2024-01-15 10:30:03 - INFO - 上传完成! 成功: 2, 失败: 0
```

## 错误处理

脚本包含完善的错误处理机制：

- **连接错误**: 自动重试连接 OBS
- **上传失败**: 每个文件最多重试 3 次
- **文件不存在**: 跳过并记录错误
- **权限问题**: 记录详细错误信息
- **网络中断**: 指数退避重试

## 安全注意事项

⚠️ **重要**: 当前脚本中包含了硬编码的 OBS 凭证。在生产环境中，建议：

1. 使用环境变量存储敏感信息
2. 使用配置文件（不提交到版本控制）
3. 使用华为云 IAM 角色和临时凭证

## 自定义配置

如需修改配置，请编辑脚本中的 `OBSConfig` 类：

```python
class OBSConfig:
    def __init__(self):
        self.access_key = 'YOUR_ACCESS_KEY'
        self.secret_key = 'YOUR_SECRET_KEY'
        self.endpoint = 'YOUR_ENDPOINT'
        self.bucket = 'YOUR_BUCKET'
        # ...
```

## 故障排除

### 常见问题

1. **ImportError: No module named 'obs'**
   ```bash
   pip install esdk-obs-python
   ```

2. **连接超时**
   - 检查网络连接
   - 验证 endpoint 地址
   - 确认防火墙设置

3. **权限被拒绝**
   - 验证 Access Key 和 Secret Key
   - 检查 IAM 权限配置
   - 确认 Bucket 访问权限

4. **文件上传失败**
   - 检查文件是否存在
   - 验证文件大小限制
   - 查看详细错误日志

### 调试模式

要启用更详细的日志，可以修改日志级别：

```python
logging.basicConfig(level=logging.DEBUG)
```

## 扩展功能

脚本可以轻松扩展以支持：

- 上传其他类型的文件
- 自定义上传路径
- 并发上传
- 增量上传（跳过已存在的文件）
- 文件压缩
- 上传进度条

## 许可证

本脚本基于现有项目的配置和逻辑开发，遵循项目的许可证条款。
