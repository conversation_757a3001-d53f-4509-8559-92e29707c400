#!/usr/bin/env python3
"""
OBS 连接测试脚本
用于验证 OBS 配置和连接是否正常
"""

import sys

try:
    from obs import ObsClient
except ImportError:
    print("❌ 错误: 请先安装华为云 OBS SDK")
    print("运行: pip install esdk-obs-python")
    sys.exit(1)

class OBSConfig:
    """OBS 配置类"""
    def __init__(self):
        self.access_key = 'VOG0TZ1NKSUHGPSWA6HS'
        self.secret_key = 'gzUzXAuFtq2rkBixaZAFnjCYOGii393SiYQeXMW0'
        self.endpoint = 'obs.ap-southeast-1.myhuaweicloud.com'
        self.bucket = 'satworld-resource'
        self.region = 'ap-southeast-1'

def test_obs_connection():
    """测试 OBS 连接"""
    print("🔧 OBS 连接测试")
    print("=" * 50)
    
    config = OBSConfig()
    
    print(f"📍 Endpoint: {config.endpoint}")
    print(f"🪣 Bucket: {config.bucket}")
    print(f"🌍 Region: {config.region}")
    print(f"🔑 Access Key: {config.access_key[:8]}...")
    print()
    
    try:
        # 创建 OBS 客户端
        print("🔌 正在创建 OBS 客户端...")
        obs_client = ObsClient(
            access_key_id=config.access_key,
            secret_access_key=config.secret_key,
            server=f'https://{config.endpoint}'
        )
        print("✅ OBS 客户端创建成功")
        
        # 测试列出存储桶
        print("📋 正在测试列出存储桶...")
        resp = obs_client.listBuckets()
        
        print(f"📊 响应状态码: {resp.status}")
        print(f"📊 响应头: {resp.header}")
        
        if resp.status < 300:
            print("✅ 成功连接到 OBS!")
            if hasattr(resp.body, 'buckets') and resp.body.buckets:
                print(f"🪣 找到 {len(resp.body.buckets)} 个存储桶:")
                for bucket in resp.body.buckets:
                    print(f"  - {bucket.name} (创建时间: {bucket.creation_date})")
            else:
                print("⚠️  没有找到存储桶")
        else:
            print(f"❌ 连接失败: {resp.errorCode} - {resp.errorMessage}")
            return False
        
        # 测试访问指定存储桶
        print(f"\n🪣 正在测试访问存储桶 '{config.bucket}'...")
        resp = obs_client.headBucket(config.bucket)
        
        print(f"📊 存储桶响应状态码: {resp.status}")
        
        if resp.status < 300:
            print(f"✅ 成功访问存储桶 '{config.bucket}'")
        else:
            print(f"❌ 访问存储桶失败: {resp.errorCode} - {resp.errorMessage}")
            return False
        
        # 测试列出对象（限制数量）
        print(f"\n📁 正在测试列出存储桶中的对象...")
        resp = obs_client.listObjects(
            bucketName=config.bucket,
            maxKeys=5  # 只列出前5个对象
        )
        
        print(f"📊 列出对象响应状态码: {resp.status}")
        
        if resp.status < 300:
            print("✅ 成功列出对象")
            if hasattr(resp.body, 'contents') and resp.body.contents:
                print(f"📄 找到对象 (显示前5个):")
                for obj in resp.body.contents[:5]:
                    size_kb = obj.size / 1024
                    print(f"  - {obj.key} ({size_kb:.2f} KB)")
            else:
                print("📄 存储桶为空或没有对象")
        else:
            print(f"❌ 列出对象失败: {resp.errorCode} - {resp.errorMessage}")
            return False
        
        # 关闭客户端
        obs_client.close()
        print("\n🔌 OBS 客户端连接已关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接测试发生异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("🧪 华为云 OBS 连接测试工具")
    print("=" * 50)
    
    success = test_obs_connection()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过! OBS 连接正常")
        print("💡 现在可以运行实际的上传脚本了")
        return 0
    else:
        print("❌ 连接测试失败")
        print("💡 请检查:")
        print("   1. 网络连接是否正常")
        print("   2. Access Key 和 Secret Key 是否正确")
        print("   3. 存储桶名称是否正确")
        print("   4. 是否有足够的权限")
        return 1

if __name__ == "__main__":
    sys.exit(main())
