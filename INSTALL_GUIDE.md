# JSON 文件上传工具 - 安装和使用指南

## 📋 概述

本工具基于现有的 `obsService.js` 逻辑，使用 Python 实现了将当前目录下所有 `.json` 文件上传到华为云 OBS 的功能。

## 🎯 目标文件

当前目录下找到的 JSON 文件：
- `package.json` (0.76 KB)
- `package-lock.json` (120.21 KB)

## 🚀 快速开始

### 1. 检查 Python 环境

```bash
# 检查 Python 版本（需要 3.6+）
python3 --version

# 或者
python --version
```

### 2. 安装依赖

**重要**: 如果遇到 "externally-managed-environment" 错误，需要使用虚拟环境：

```bash
# 创建虚拟环境
python3 -m venv obs_upload_env

# 激活虚拟环境
source obs_upload_env/bin/activate  # Linux/macOS
# 或者在 Windows 上：
# obs_upload_env\Scripts\activate

# 安装依赖
pip install esdk-obs-python

# 验证安装
python -c "import obs; print('✅ OBS SDK 安装成功!')"
```

**传统方法**（如果系统允许）：
```bash
# 方法一：使用 requirements.txt
pip3 install -r requirements.txt

# 方法二：直接安装 OBS SDK
pip3 install esdk-obs-python

# 方法三：使用 --user 标志
pip3 install --user esdk-obs-python
```

### 3. 运行测试（推荐）

```bash
# 先运行测试脚本，验证文件扫描逻辑
python3 quick_upload.py
```

### 4. 执行实际上传

**如果使用虚拟环境**：
```bash
# 激活虚拟环境（如果还没激活）
source obs_upload_env/bin/activate

# 运行完整的上传脚本
python upload_json_files.py

# 完成后退出虚拟环境
deactivate
```

**如果使用系统 Python**：
```bash
# 运行完整的上传脚本
python3 upload_json_files.py
```

## 📁 文件说明

| 文件名 | 描述 |
|--------|------|
| `upload_json_files.py` | 主上传脚本，包含完整功能 |
| `quick_upload.py` | 快速测试脚本，模拟上传过程 |
| `requirements.txt` | Python 依赖列表 |
| `README_upload.md` | 详细使用说明 |
| `INSTALL_GUIDE.md` | 本安装指南 |

## 🔧 配置信息

脚本使用以下 OBS 配置（与 `src/utils/obsEnv.js` 一致）：

```
Bucket: satworld-resource
Endpoint: obs.ap-southeast-1.myhuaweicloud.com
Region: ap-southeast-1
Target Path: website_public/json_files/
```

## 📊 预期结果

上传成功后，文件将存储在：
- `website_public/json_files/package.json`
- `website_public/json_files/package-lock.json`

## 🛠️ 故障排除

### 常见问题

1. **externally-managed-environment 错误**
   ```bash
   # 这是 macOS/Linux 上的常见问题，使用虚拟环境解决
   python3 -m venv obs_upload_env
   source obs_upload_env/bin/activate
   pip install esdk-obs-python
   ```

2. **Python 版本问题**
   ```bash
   # 如果系统默认是 Python 2，使用 python3
   python3 upload_json_files.py
   ```

3. **依赖安装失败**
   ```bash
   # 使用国内镜像源
   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple esdk-obs-python

   # 或者使用清华源
   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ esdk-obs-python
   ```

4. **权限问题**
   ```bash
   # 使用用户安装（不推荐，建议使用虚拟环境）
   pip3 install --user esdk-obs-python

   # 或者强制安装（有风险）
   pip3 install --break-system-packages esdk-obs-python
   ```

5. **虚拟环境相关问题**
   ```bash
   # 删除损坏的虚拟环境
   rm -rf obs_upload_env

   # 重新创建
   python3 -m venv obs_upload_env
   source obs_upload_env/bin/activate
   pip install esdk-obs-python
   ```

4. **网络连接问题**
   - 检查网络连接
   - 确认可以访问华为云服务
   - 验证防火墙设置

### 调试步骤

1. **验证文件扫描**
   ```bash
   # 如果使用虚拟环境
   source obs_upload_env/bin/activate
   python quick_upload.py

   # 如果使用系统 Python
   python3 quick_upload.py
   ```

2. **检查依赖**
   ```bash
   # 如果使用虚拟环境
   source obs_upload_env/bin/activate
   python -c "import obs; print('OBS SDK 安装成功')"

   # 如果使用系统 Python
   python3 -c "import obs; print('OBS SDK 安装成功')"
   ```

3. **检查虚拟环境状态**
   ```bash
   # 查看当前是否在虚拟环境中
   echo $VIRTUAL_ENV

   # 查看 Python 路径
   which python

   # 查看已安装的包
   pip list | grep obs
   ```

3. **查看详细日志**
   - 运行后检查 `upload_log.txt` 文件
   - 查看 `upload_report.txt` 报告

## 📈 执行流程

1. **初始化** - 连接 OBS 服务
2. **扫描** - 查找当前目录下的 JSON 文件
3. **上传** - 逐个上传文件到 OBS
4. **验证** - MD5 校验确保文件完整性
5. **报告** - 生成详细的上传报告

## 🔒 安全提醒

⚠️ **注意**: 脚本中包含 OBS 访问凭证，请确保：
- 不要将凭证提交到公共代码仓库
- 定期轮换访问密钥
- 使用最小权限原则

## 📞 支持

如果遇到问题：
1. 查看 `upload_log.txt` 日志文件
2. 检查网络连接和权限设置
3. 验证 OBS 配置信息
4. 参考华为云 OBS 官方文档

## 🎉 成功标志

看到以下输出表示上传成功：
```
上传完成! 成功: 2, 失败: 0
所有文件上传成功!
```

上传完成后，可以在华为云 OBS 控制台的 `satworld-resource` 存储桶中查看上传的文件。
