#!/usr/bin/env python3
"""
华为云 OBS JSON 文件上传工具 - 演示版本
模拟上传过程，展示完整功能，无需实际 OBS 连接
"""

import os
import glob
import time
import hashlib
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('upload_log_demo.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OBSConfig:
    """OBS 配置类，对应 obsEnv.js 中的配置"""
    def __init__(self):
        self.access_key = 'VOG0TZ1NKSUHGPSWA6HS'
        self.secret_key = 'gzUzXAuFtq2rkBixaZAFnjCYOGii393SiYQeXMW0'
        self.endpoint = 'obs.ap-southeast-1.myhuaweicloud.com'
        self.bucket = 'satworld-resource'
        self.region = 'ap-southeast-1'
        self.service = 'obs'

class JSONFileUploaderDemo:
    """JSON 文件上传器 - 演示版本"""
    
    def __init__(self, config: OBSConfig):
        self.config = config
        self.upload_results = []
        
    def initialize_client(self) -> bool:
        """模拟初始化 OBS 客户端"""
        try:
            logger.info("🔌 模拟连接到 OBS...")
            time.sleep(1)  # 模拟连接时间
            logger.info(f"✅ 成功连接到 OBS 模拟环境")
            logger.info(f"📍 Endpoint: {self.config.endpoint}")
            logger.info(f"🪣 Bucket: {self.config.bucket}")
            return True
                
        except Exception as e:
            logger.error(f"初始化 OBS 客户端失败: {str(e)}")
            return False
    
    def find_json_files(self, directory: str = ".") -> List[str]:
        """查找指定目录下的所有 .json 文件（排除 node_modules）"""
        json_files = []
        
        # 使用 glob 查找 .json 文件
        pattern = os.path.join(directory, "*.json")
        files = glob.glob(pattern)
        
        # 过滤掉 node_modules 目录下的文件
        for file_path in files:
            if "node_modules" not in file_path:
                json_files.append(file_path)
        
        logger.info(f"找到 {len(json_files)} 个 JSON 文件: {json_files}")
        return json_files
    
    def calculate_file_md5(self, file_path: str) -> str:
        """计算文件的 MD5 哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件 {file_path} MD5 失败: {str(e)}")
            return ""
    
    def retry_operation(self, operation, max_retries: int = 3, delay: float = 1.0, operation_name: str = "操作"):
        """通用重试机制，对应 obsService.js 中的 retry 函数"""
        for attempt in range(max_retries):
            try:
                result = operation()
                return result
            except Exception as error:
                if attempt == max_retries - 1:
                    logger.error(f"{operation_name}失败，已达最大重试次数。最终错误: {str(error)}")
                    raise error
                
                logger.warning(f"{operation_name}发生错误，将在 {delay}s 后重试 ({max_retries - attempt - 1} 次剩余)。错误: {str(error)}")
                time.sleep(delay)
                delay = min(delay * 2, 30)  # 指数退避，最大延迟30秒
    
    def upload_single_file(self, file_path: str, object_key: str = None) -> Dict:
        """
        模拟上传单个文件到 OBS
        
        Args:
            file_path: 本地文件路径
            object_key: OBS 对象键，如果为 None 则使用文件名
            
        Returns:
            包含上传结果的字典
        """
        if object_key is None:
            object_key = f"website_public/json_files/{os.path.basename(file_path)}"
        
        file_size = os.path.getsize(file_path)
        file_md5 = self.calculate_file_md5(file_path)
        
        logger.info(f"准备上传文件: '{file_path}' 到 '{object_key}' (大小: {file_size} 字节)")
        
        def upload_operation():
            # 模拟上传过程
            logger.info(f"📤 正在上传 {os.path.basename(file_path)}...")
            
            # 模拟上传时间（根据文件大小）
            upload_time = min(file_size / 50000, 3.0)  # 最多3秒
            time.sleep(upload_time)
            
            # 模拟成功上传
            logger.info(f"✅ 文件上传完成: '{object_key}'")
            return {
                'key': object_key,
                'status': 'success',
                'local_path': file_path,
                'size': file_size,
                'md5': file_md5,
                'etag': f'"{file_md5}"',  # 模拟 ETag
                'upload_time': upload_time
            }
        
        try:
            result = self.retry_operation(
                upload_operation,
                max_retries=3,
                delay=1.0,
                operation_name=f"上传文件 '{file_path}' 到 '{object_key}'"
            )
            return result
        except Exception as e:
            error_msg = f"上传文件 '{file_path}' 发生异常: {str(e)}"
            logger.error(error_msg)
            return {
                'key': object_key,
                'status': 'failure',
                'local_path': file_path,
                'error': error_msg
            }
    
    def upload_all_json_files(self, directory: str = ".") -> List[Dict]:
        """上传所有 JSON 文件"""
        json_files = self.find_json_files(directory)
        
        if not json_files:
            logger.warning("没有找到需要上传的 JSON 文件")
            return []
        
        logger.info(f"开始上传 {len(json_files)} 个 JSON 文件...")
        
        results = []
        success_count = 0
        failure_count = 0
        total_size = 0
        total_time = 0
        
        for i, file_path in enumerate(json_files, 1):
            logger.info(f"[{i}/{len(json_files)}] 正在处理: {file_path}")
            
            # 生成对象键，保持文件的相对路径结构
            relative_path = os.path.relpath(file_path, directory)
            object_key = f"website_public/json_files/{relative_path}"
            
            result = self.upload_single_file(file_path, object_key)
            results.append(result)
            
            if result['status'] == 'success':
                success_count += 1
                total_size += result.get('size', 0)
                total_time += result.get('upload_time', 0)
                logger.info(f"✅ 成功上传: {file_path}")
            else:
                failure_count += 1
                logger.error(f"❌ 上传失败: {file_path} - {result.get('error', '未知错误')}")
            
            # 在文件之间添加短暂延迟，避免请求过于密集
            if i < len(json_files):
                time.sleep(0.1)
        
        # 显示统计信息
        total_size_mb = total_size / 1024 / 1024
        avg_speed = total_size / total_time if total_time > 0 else 0
        avg_speed_kbps = avg_speed / 1024
        
        logger.info(f"📊 上传统计:")
        logger.info(f"   ✅ 成功: {success_count}")
        logger.info(f"   ❌ 失败: {failure_count}")
        logger.info(f"   📦 总大小: {total_size_mb:.2f} MB")
        logger.info(f"   ⏱️  总时间: {total_time:.2f} 秒")
        logger.info(f"   🚀 平均速度: {avg_speed_kbps:.2f} KB/s")
        
        self.upload_results = results
        return results
    
    def generate_upload_report(self) -> str:
        """生成上传报告"""
        if not self.upload_results:
            return "没有上传结果可报告"
        
        report_lines = [
            "=" * 60,
            "JSON 文件上传报告 (演示模式)",
            "=" * 60,
            f"上传时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"目标存储桶: {self.config.bucket}",
            f"目标端点: {self.config.endpoint}",
            f"模式: 演示模式 (未实际上传到 OBS)",
            ""
        ]
        
        success_files = [r for r in self.upload_results if r['status'] == 'success']
        failure_files = [r for r in self.upload_results if r['status'] == 'failure']
        
        total_size = sum(r.get('size', 0) for r in success_files)
        total_time = sum(r.get('upload_time', 0) for r in success_files)
        
        report_lines.extend([
            f"总文件数: {len(self.upload_results)}",
            f"成功上传: {len(success_files)}",
            f"上传失败: {len(failure_files)}",
            f"总大小: {total_size / 1024 / 1024:.2f} MB",
            f"总时间: {total_time:.2f} 秒",
            f"平均速度: {(total_size / total_time / 1024):.2f} KB/s" if total_time > 0 else "平均速度: N/A",
            ""
        ])
        
        if success_files:
            report_lines.extend([
                "成功上传的文件:",
                "-" * 40
            ])
            for result in success_files:
                size_mb = result.get('size', 0) / 1024 / 1024
                upload_time = result.get('upload_time', 0)
                report_lines.append(f"✅ {result['local_path']} -> {result['key']}")
                report_lines.append(f"   📊 大小: {size_mb:.2f} MB, 时间: {upload_time:.2f}s, MD5: {result.get('md5', 'N/A')[:8]}...")
            report_lines.append("")
        
        if failure_files:
            report_lines.extend([
                "上传失败的文件:",
                "-" * 40
            ])
            for result in failure_files:
                report_lines.append(f"❌ {result['local_path']} -> {result['key']}")
                report_lines.append(f"   错误: {result.get('error', '未知错误')}")
            report_lines.append("")
        
        report_lines.extend([
            "注意事项:",
            "-" * 40,
            "⚠️  这是演示模式，文件并未实际上传到华为云 OBS",
            "🔧 要进行实际上传，请:",
            "   1. 确保 OBS 凭证正确且有效",
            "   2. 验证网络连接和权限",
            "   3. 使用 upload_json_files.py 脚本",
            ""
        ])
        
        report_lines.append("=" * 60)
        return "\n".join(report_lines)
    
    def save_upload_report(self, filename: str = "upload_report_demo.txt"):
        """保存上传报告到文件"""
        report = self.generate_upload_report()
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"上传报告已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存上传报告失败: {str(e)}")
    
    def cleanup(self):
        """清理资源"""
        logger.info("🔌 模拟关闭 OBS 客户端连接")

def main():
    """主函数"""
    logger.info("🎭 开始执行 JSON 文件上传任务 (演示模式)...")
    
    # 初始化配置
    config = OBSConfig()
    uploader = JSONFileUploaderDemo(config)
    
    try:
        # 初始化 OBS 客户端
        if not uploader.initialize_client():
            logger.error("无法初始化 OBS 客户端，程序退出")
            return 1
        
        # 上传所有 JSON 文件
        results = uploader.upload_all_json_files()
        
        # 生成并显示报告
        report = uploader.generate_upload_report()
        print("\n" + report)
        
        # 保存报告到文件
        uploader.save_upload_report()
        
        # 检查是否有失败的上传
        failure_count = len([r for r in results if r['status'] == 'failure'])
        if failure_count > 0:
            logger.warning(f"有 {failure_count} 个文件上传失败，请检查日志")
            return 1
        else:
            logger.info("🎉 所有文件上传成功! (演示模式)")
            return 0
            
    except KeyboardInterrupt:
        logger.info("用户中断了上传过程")
        return 1
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {str(e)}")
        return 1
    finally:
        uploader.cleanup()

if __name__ == "__main__":
    print("🎭 JSON 文件上传工具 - 演示版本")
    print("⚠️  注意: 这是演示模式，不会实际上传文件到 OBS")
    print("=" * 60)
    exit(main())
