#!/bin/bash

# JSON 文件上传工具 - Linux/macOS 版本

echo "===================================================="
echo "📦 JSON 文件上传工具"
echo "===================================================="
echo

# 检查 Python 环境
echo "🔍 检查 Python 环境..."
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    PIP_CMD="pip3"
    echo "✅ 找到 Python3: $(python3 --version)"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
    PIP_CMD="pip"
    echo "✅ 找到 Python: $(python --version)"
else
    echo "❌ 未找到 Python，请先安装 Python 3.6+"
    exit 1
fi

echo

# 检查依赖
echo "📦 检查依赖..."
if $PYTHON_CMD -c "import obs" &> /dev/null; then
    echo "✅ OBS SDK 已安装"
else
    echo "⚠️  未找到 OBS SDK，正在安装..."
    $PIP_CMD install esdk-obs-python
    if [ $? -eq 0 ]; then
        echo "✅ 依赖安装成功"
    else
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

echo

# 运行测试（可选）
echo "🧪 是否先运行测试？(y/N)"
read -r response
if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    echo "🚀 运行测试脚本..."
    $PYTHON_CMD quick_upload.py
    echo
    echo "📋 测试完成，是否继续实际上传？(y/N)"
    read -r continue_response
    if [[ ! "$continue_response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        echo "❌ 用户取消上传"
        exit 0
    fi
fi

echo

# 执行上传
echo "🚀 开始上传 JSON 文件..."
$PYTHON_CMD upload_json_files.py

echo

# 显示报告
echo "📊 查看上传报告..."
if [ -f "upload_report.txt" ]; then
    cat upload_report.txt
else
    echo "⚠️  未找到上传报告文件"
fi

echo
echo "✅ 脚本执行完成"
