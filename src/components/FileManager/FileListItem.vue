<template>
  <li
    class="group hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150"
    tabindex="0"
    @keydown.enter="handleItemClick"
  >
    <div class="px-4 py-3 flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div v-if="isSelecting" class="flex-shrink-0">
          <input
            type="checkbox"
            class="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            :checked="isSelected"
            @change="$emit('toggle-selection', item)"
          />
        </div>
        <div
          class="flex items-center space-x-3 cursor-pointer"
          @click="handleItemClick"
        >
          <FolderIcon
            v-if="item.type === 'directory'"
            class="h-6 w-6 text-primary-500"
          />
          <DocumentIcon
            v-else
            class="h-6 w-6 text-gray-400"
          />
          <div class="flex flex-col">
            <div class="flex items-center">
              <span class="text-gray-900 dark:text-gray-100 font-medium">{{ item.name }}</span>
              <span
                v-if="item.type === 'directory'"
                class="inline-flex items-center px-2 py-0.5 ml-2 rounded-full bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-300 text-xs font-semibold shadow-sm"
                title="目录"
              >
                <FolderIcon class="w-3 h-3 mr-1 text-primary-400 dark:text-primary-300" />
                目录
              </span>
            </div>
            <div v-if="item.type === 'file' && item.lastModified" class="flex items-center space-x-2 mt-1">
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ formatLastModified(item.lastModified) }}
              </span>
              <span v-if="item.size" class="text-xs text-gray-500 dark:text-gray-400">
                · {{ formatFileSize(item.size) }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div v-if="!isSelecting" class="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <button
          v-if="item.type !== 'directory'"
          class="p-2 rounded-lg text-gray-400 hover:text-primary-600 dark:hover:text-primary-400"
          @click.stop="$emit('download', item)"
          title="下载"
        >
          <ArrowDownTrayIcon class="h-5 w-5" />
        </button>
        <button
          class="p-2 rounded-lg text-gray-400 hover:text-red-600 dark:hover:text-red-400"
          @click.stop="$emit('delete', item)"
          title="删除"
        >
          <TrashIcon class="h-5 w-5" />
        </button>
        <button
          v-if="item.type === 'directory'"
          class="p-2 rounded-lg text-gray-400 hover:text-primary-600 dark:hover:text-primary-400"
          @click.stop="$emit('copy', item)"
          title="复制目录"
        >
          <DocumentDuplicateIcon class="h-5 w-5" />
        </button>
      </div>
    </div>
  </li>
</template>

<script setup>
import {
  FolderIcon,
  DocumentIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  DocumentDuplicateIcon,
} from '@heroicons/vue/24/outline'
import { formatLastModified, formatFileSize } from './utils'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  isSelecting: {
    type: Boolean,
    required: true
  },
  isSelected: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['toggle-selection', 'navigate', 'preview', 'download', 'delete', 'copy'])

const handleItemClick = () => {
  if (props.isSelecting) {
    emit('toggle-selection', props.item)
  } else if (props.item.type === 'directory') {
    emit('navigate', props.item)
  } else {
    emit('preview', props.item)
  }
}
</script>