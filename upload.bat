@echo off
chcp 65001 >nul
echo ====================================================
echo JSON 文件上传工具
echo ====================================================
echo.

echo 🔍 检查 Python 环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到 Python，请先安装 Python 3.6+
    pause
    exit /b 1
)

echo ✅ Python 环境正常

echo.
echo 📦 检查依赖...
python -c "import obs" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  未找到 OBS SDK，正在安装...
    pip install esdk-obs-python
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装成功
) else (
    echo ✅ 依赖已安装
)

echo.
echo 🚀 开始上传 JSON 文件...
python upload_json_files.py

echo.
echo 📊 查看上传报告...
if exist upload_report.txt (
    type upload_report.txt
) else (
    echo ⚠️  未找到上传报告文件
)

echo.
echo 按任意键退出...
pause >nul
